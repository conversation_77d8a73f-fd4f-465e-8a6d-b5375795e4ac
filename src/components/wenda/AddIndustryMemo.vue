<template>
  <div class="add-industry-memo-overlay">
    <div class="dialog-container memo-dialog">
      <div class="dialog-header">
        <div class="dialog-title">
          <EditIcon :size="32" color="var(--primary-color)" />
          <span>添加行业备忘录</span>
        </div>
        <div class="dialog-close" @click="handleClose">
          <DeleteIcon :size="24" color="var(--primary-color)" />
        </div>
      </div>
      
      <div class="dialog-content memo-content">
        <!-- 便捷输入部分 -->
        <div class="input-group">
          <label class="input-label">便捷输入</label>
          <div class="quick-input-container">
            <button
              v-for="(item, index) in quickInputItems"
              :key="index"
              class="quick-input-btn"
              @click="handleQuickInput(item.text)"
            >
              {{ item.label }}
            </button>
          </div>
        </div>

        <!-- 备忘录内容输入 -->
        <div class="input-group">
          <label class="input-label">备忘录内容</label>
          <div class="textarea-wrapper">
            <textarea
              ref="memoInputRef"
              v-model="memoContent"
              class="textarea-field with-voice"
              placeholder="请输入行业备忘录内容，比如：行业趋势分析..."
              rows="8"
              maxlength="1000"
            ></textarea>
            <!-- 语音按钮在多行输入框内部右上角 -->
            <div
              class="voice-toggle-inner textarea-voice"
              :class="{
                breathing: isRecording,
              }"
              @click="handleVoiceButtonClick"
            >
              <MicrophoneIcon :size="16" />
            </div>
          </div>
          <div class="char-count">{{ memoContent.length }}/1000</div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="dialog-footer">
        <button class="cancel-btn" @click="handleClose">取消</button>
        <button
          class="confirm-btn"
          :disabled="!memoContent.trim() || isSaving"
          @click="handleSave"
        >
          {{ isSaving ? '保存中...' : '保存' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount, nextTick } from 'vue';
import { showToast } from 'vant';
import EditIcon from '@/assets/icons/EditIcon.vue';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';
import MicrophoneIcon from '@/assets/icons/MicrophoneIcon.vue';
import { getStreamAsr } from '@/apis/chat';
import Recorder from 'recorder-realtime';
import { generateRandomString } from '@/utils';

// Props定义
interface IProps {
  userId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  save: [content: string];
}>();

// 响应式数据
const memoContent = ref('');
const isSaving = ref(false);

// 便捷输入选项 - 针对行业备忘录的内容
const quickInputItems = [
  { label: '行业趋势', text: '当前行业趋势：' },
  { label: '竞争对手', text: '主要竞争对手：' },
  { label: '市场分析', text: '市场分析：' },
  { label: '技术发展', text: '技术发展方向：' },
  { label: '政策影响', text: '相关政策影响：' },
  { label: '重要事件', text: '行业重要事件：' },
];

// 语音相关数据
const micPermission = ref(false);
const sessionId = ref('');
const audioBufferIndex = ref(0);
const lastBuffer = ref();
const voiceMessage = ref('');
const isRecording = ref(false);
const lastVoiceText = ref('');

// 录音相关变量
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let timerId: ReturnType<typeof setTimeout> | null = null;
let mediaStream: MediaStream | null = null;

// 输入框引用
const memoInputRef = ref();

// 便捷输入处理
const handleQuickInput = (text: string) => {
  const currentContent = memoContent.value;
  const newContent = currentContent ? `${currentContent}\n${text}` : text;
  memoContent.value = newContent;

  // 聚焦到输入框末尾
  void nextTick(() => {
    if (memoInputRef.value) {
      memoInputRef.value.focus();
      const { length } = memoContent.value;
      memoInputRef.value.setSelectionRange(length, length);
    }
  });
};

// 处理关闭
const handleClose = () => {
  // 重置表单
  memoContent.value = '';
  emit('close');
};

// 处理保存
const handleSave = async () => {
  if (!memoContent.value.trim()) {
    showToast('请输入备忘录内容');
    return;
  }

  try {
    isSaving.value = true;

    // TODO: 这里后续需要调用后端API保存备忘录
    console.log('🔄 [AddIndustryMemo] 保存备忘录:', {
      userId: props.userId,
      content: memoContent.value.trim(),
    });

    // 模拟保存延迟
    await new Promise<void>(resolve => {
      setTimeout(resolve, 1000);
    });

    // 通知父组件保存成功
    emit('save', memoContent.value.trim());

    // 关闭对话框
    handleClose();

    showToast('行业备忘录保存成功');
  } catch (error) {
    console.error('❌ [AddIndustryMemo] 保存备忘录失败:', error);
    showToast('保存失败，请重试');
  } finally {
    isSaving.value = false;
  }
};

// 语音相关方法
// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => track.stop());
    mediaStream = null;
  }
};

// 设置麦克风权限
async function setMicPermission() {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
}

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
    return;
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    cancelRecording();
  };

  recorder.ondataavailable = async (data: ArrayBuffer) => {
    if (isRecording.value) {
      lastBuffer.value = data;
      audioBufferIndex.value += 1;

      try {
        const streamData = await getStreamAsr({
          sessionId: sessionId.value,
          format: 'pcm',
          sampleRate: 16000,
          index: audioBufferIndex.value,
          data: Array.from(new Uint8Array(data)),
        });

        // 修复：检查 full_text 而不是 text，并且确保 full_text 不为空且与当前值不同
        if (
          streamData.data.full_text &&
          streamData.data.full_text.trim() !== '' &&
          streamData.data.full_text !== lastVoiceText.value
        ) {
          // 计算新增的文字部分
          const newText = streamData.data.full_text;
          const previousText = lastVoiceText.value;

          // 如果新文字包含之前的文字，只插入新增部分
          let textToInsert = newText;
          if (previousText && newText.startsWith(previousText)) {
            textToInsert = newText.slice(previousText.length);
          }

          // 在光标位置插入新文字
          if (textToInsert) {
            insertTextAtCursor(textToInsert);
          }

          lastVoiceText.value = newText;
          voiceMessage.value = newText;
        }
      } catch (error) {
        console.error('❌ [AddIndustryMemo] 语音识别失败:', error);
      }
    }
  };
};



// 在光标位置插入文字
const insertTextAtCursor = (newText: string) => {
  if (!memoInputRef.value) return;

  const inputElement = memoInputRef.value;
  const start = (inputElement.selectionStart as number) || 0;
  const end = (inputElement.selectionEnd as number) || 0;
  const currentValue = memoContent.value;

  const newValue = currentValue.slice(0, start) + newText + currentValue.slice(end);
  memoContent.value = newValue;

  const newCursorPosition = start + newText.length;

  void nextTick(() => {
    inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
    inputElement.focus();
  });
};

// 开始录音
async function startRecording() {
  if (isRecording.value) {
    await stopRecording();
    return;
  }

  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    lastVoiceText.value = '';
    voiceMessage.value = '';

    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopRecording();
    }, 1000 * 60);
  }
}

// 停止录音
async function stopRecording() {
  if (!isRecording.value) return;

  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  // 释放麦克风资源
  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });

  if (voiceMessage.value) {
    // 语音识别完成，文字已经通过 insertTextAtCursor 插入到光标位置
    console.log('📤 [AddIndustryMemo] 语音识别完成，文字已插入到光标位置:', voiceMessage.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }
  // 清空语音消息和上次识别文字
  voiceMessage.value = '';
  lastVoiceText.value = '';
}

// 取消录音
const cancelRecording = () => {
  if (isRecording.value) {
    isRecording.value = false;
    if (timerId !== null) {
      clearTimeout(timerId);
      timerId = null;
    }
    if (recorder) {
      recorder.stop();
    }
    releaseMicrophoneResources();
    voiceMessage.value = '';
    lastVoiceText.value = '';
  }
};

// 处理语音按钮点击
const handleVoiceButtonClick = async () => {
  await startRecording();
};

// 组件卸载时释放麦克风资源
onBeforeUnmount(() => {
  console.log('🧹 [AddIndustryMemo] 组件卸载，释放麦克风资源');
  if (isRecording.value) {
    if (recorder) {
      recorder.stop();
    }
    isRecording.value = false;
  }
  releaseMicrophoneResources();
});
</script>

<style lang="scss" scoped>
// 对话框样式
.add-industry-memo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  backdrop-filter: blur(20px);
  display: flex;
  width: 100%;
  align-items: stretch;
  justify-content: stretch;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
  pointer-events: auto;
}

.dialog-container {
  position: relative;
  background: var(--bg-glass-popup);
  border: 1px solid var(--bg-glass-light);
  border-radius: 20px;
  padding: 30px;
  padding-bottom: 120px;
  box-sizing: border-box;
  backdrop-filter: blur(15px);
  border-left: none;
  box-shadow:
    0 8px 32px var(--overlay-dark),
    0 0 0 1px var(--bg-input);
  transition: all 0.3s ease;
  width: 100%;
  height: calc(100% - 150px);
  color: var(--text-primary);
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &.memo-dialog {
    max-width: none;
    height: calc(100% - 150px);
    overflow-y: auto;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 13px;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #000000;
    font-size: 40px;
    font-weight: 600;
  }

  .dialog-close {
    background: var(--bg-glass-light);
    border: 1px solid var(--bg-glass-hover);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: var(--bg-glass-hover);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }
  }
}

.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 60px;

  &.memo-content {
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--bg-glass-light);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;

      &:hover {
        background: var(--text-quaternary);
      }
    }
  }

  .input-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    border: none;
    border-radius: 16px;
    padding: 22px;
    background: var(--bg-glass-light);
    backdrop-filter: blur(10px);
    border-left: 4px solid var(--accent-color);
    box-shadow: var(--shadow-accent);
    transition: all 0.3s ease;

    .input-label {
      color: #000000;
      font-size: 30px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    // 便捷输入容器
    .quick-input-container {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
    }

    .quick-input-btn {
      padding: 12px 16px;
      background: var(--bg-glass-light);
      border: 2px solid var(--border-accent);
      border-radius: 12px;
      color: var(--text-primary);
      font-size: 24px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);

      &:hover {
        background: var(--primary-color-medium);
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
      }
    }

    .textarea-wrapper {
      position: relative;
    }

    .textarea-field {
      width: 100%;
      background: var(--bg-glass-light);
      border: 2px solid var(--border-accent);
      border-radius: 20px;
      padding: 18px 22px;
      color: var(--person-detail-context);
      font-size: 32px;
      line-height: 1.6;
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      resize: vertical;
      min-height: 200px;
      font-family: inherit;
      transition: all 0.3s ease;

      &.with-voice {
        padding-right: 76px;
        padding-top: 24px;
      }

      &::placeholder {
        color: var(--person-detail-context);
      }

      &:focus {
        outline: none;
        border-color: var(--primary-color);
        background: var(--bg-input-focus);
        box-shadow: 0 0 0 3px var(--primary-color-medium);
      }
    }

    // 字符计数
    .char-count {
      text-align: right;
      color: var(--text-tertiary);
      font-size: 24px;
      margin-top: 8px;
    }
  }
}

// 语音按钮样式
.voice-toggle-inner {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  background: var(--bg-glass-light);
  border: 2px solid var(--border-accent);
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
  z-index: 10;

  &.breathing {
    animation: breathing 2s ease-in-out infinite;
  }

  &.textarea-voice {
    top: 24px;
    transform: none;
  }

  &:hover {
    background: var(--primary-color-medium);
    border-color: var(--primary-color);
    transform: translateY(-50%) scale(1.05);

    &.textarea-voice {
      transform: scale(1.05);
    }
  }
}

.dialog-footer {
  position: absolute;
  bottom: 20px;
  left: 30px;
  right: 30px;
  display: flex;
  gap: 20px;
  padding: 20px 30px;
  background: var(--bg-glass-light);
  backdrop-filter: blur(10px);
  border-radius: 16px;

  .cancel-btn,
  .confirm-btn {
    flex: 1;
    padding: 16px 16px;
    border-radius: 20px;
    font-size: 36px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    background: var(--bg-glass-light);
    backdrop-filter: blur(10px);
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:disabled {
      cursor: not-allowed;
    }
  }

  .cancel-btn {
    color: var(--text-tertiary);
    border-color: var(--text-tertiary);

    &:hover:not(:disabled) {
      background: var(--bg-glass-hover);
      transform: translateY(-2px);
      box-shadow: var(--shadow-medium);
    }
  }

  .confirm-btn {
    color: #000000;
    border-color: var(--primary-color);

    &:hover:not(:disabled) {
      background: var(--primary-color-medium);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px var(--border-accent);
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes breathing {
  0%,
  100% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 0 var(--primary-color);
  }
  50% {
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 0 0 10px transparent;
  }
}

// 为textarea语音按钮的呼吸动画
.voice-toggle-inner.textarea-voice.breathing {
  animation: breathing-textarea 2s ease-in-out infinite;
}

@keyframes breathing-textarea {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 var(--primary-color);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px transparent;
  }
}
</style>
